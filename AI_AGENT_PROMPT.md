# 🤖 Prompt para Agente de IA - Implementação de Configuração de Produtos

## 📋 Contexto do Projeto

Você está trabalhando em um projeto **SupGateway** - uma plataforma white-label para criação e gerenciamento de produtos digitais. O projeto utiliza:

- **Frontend:** Next.js 14+ com App Router, React, TypeScript, Tailwind CSS, Shadcn UI
- **Backend:** Hono.js, Prisma ORM, PostgreSQL
- **Arquitetura:** Monorepo com packages separados
- **Autenticação:** Better-auth
- **Pagamentos:** Múltiplos providers (Stripe, LemonSqueezy, etc.)

## 🎯 Objetivo

Implementar uma **nova interface de configuração de produtos** baseada na imagem de referência, substituindo o sistema atual de criação de produtos por uma experiência mais moderna e intuitiva.

## 📁 Arquivo de Referência Completo

**Caminho:** `/Users/<USER>/Documents/www/Gateways/white-label/supgateway/PRODUCT_CONFIGURATION_UPGRADE_PLAN.md`

Este arquivo contém:
- Análise detalhada da imagem de referência
- Plano de implementação completo (4 fases)
- Especificações de design e UX
- Alterações necessárias no schema do banco
- Estrutura de arquivos a ser criada
- Checklist de implementação
- Especificações técnicas detalhadas

## 🏗️ Estrutura Atual do Projeto

```
supgateway/
├── apps/web/                          # Frontend Next.js
│   ├── app/(saas)/app/products/       # Páginas de produtos existentes
│   └── modules/saas/products/         # Componentes de produtos
├── packages/
│   ├── api/                           # Backend Hono.js
│   ├── database/                      # Prisma schema e queries
│   ├── auth/                          # Autenticação
│   └── payments/                      # Providers de pagamento
└── config/                            # Configurações globais
```

## 🎨 Design de Referência

A interface deve replicar exatamente a imagem fornecida, que mostra:

### Layout Principal
- **Tema escuro** com sidebar à esquerda
- **Header** com logo, breadcrumbs e informações do usuário
- **Sidebar** com navegação: Ofertas, Checkouts, Configurações, Pixels, Upsell, Cupons, Afiliação, Coprodução
- **Área principal** dividida em seções de configuração

### Seções de Configuração
1. **Informações Básicas:** Upload de imagem, nome (60 chars), descrição (200 chars)
2. **Suporte ao Comprador:** Nome do vendedor, email, telefone
3. **Preferências:** Categoria, formato, idioma, moeda, página de vendas
4. **Status:** Toggle ativo/inativo
5. **Recuperação Ativa:** Configuração de recuperação de vendas

## 🛠️ Tarefas de Implementação

### Fase 1: Frontend (Prioridade Alta)
1. **Criar layout principal** (`/products/[productId]/configuracoes/page.tsx`)
2. **Implementar sidebar** com navegação e ícones
3. **Criar seções de configuração** (5 seções principais)
4. **Implementar upload de imagem** com preview
5. **Adicionar validações** e estados de formulário

### Fase 2: Backend (Prioridade Alta)
1. **Atualizar schema Prisma** com novos campos
2. **Criar migrações** do banco de dados
3. **Implementar endpoints** da API
4. **Adicionar validações** de dados

### Fase 3: Integração (Prioridade Média)
1. **Conectar frontend com API**
2. **Implementar gerenciamento de estado**
3. **Adicionar tratamento de erros**
4. **Testar fluxo completo**

## 📋 Regras de Desenvolvimento

### Código
- Use **TypeScript** para tudo
- Siga os padrões do projeto existente
- Use **Shadcn UI** para componentes
- Implemente **responsividade** mobile-first
- Use **Tailwind CSS** para estilização

### Estrutura
- Mantenha a arquitetura de packages
- Use **Server Components** quando possível
- Implemente **Client Components** apenas quando necessário
- Siga os padrões de nomenclatura do projeto

### Banco de Dados
- Use **Prisma** para todas as operações
- Crie **migrações** para mudanças no schema
- Mantenha **compatibilidade** com dados existentes
- Adicione **índices** quando necessário

## 🎯 Critérios de Sucesso

### Funcionalidade
- [ ] Interface idêntica à imagem de referência
- [ ] Todas as seções funcionais
- [ ] Validações de formulário
- [ ] Upload de imagem funcionando
- [ ] Salvamento de dados
- [ ] Navegação entre seções

### Qualidade
- [ ] Código limpo e bem documentado
- [ ] Componentes reutilizáveis
- [ ] Tratamento de erros
- [ ] Estados de loading
- [ ] Responsividade completa

### Integração
- [ ] Funciona com sistema existente
- [ ] Não quebra funcionalidades atuais
- [ ] Dados persistidos corretamente
- [ ] API endpoints funcionais

## 🚀 Instruções de Início

1. **Leia o arquivo completo:** `PRODUCT_CONFIGURATION_UPGRADE_PLAN.md`
2. **Analise a estrutura atual** do projeto
3. **Comece pela Fase 1** (Frontend)
4. **Implemente seção por seção** seguindo o plano
5. **Teste cada funcionalidade** antes de prosseguir
6. **Mantenha o foco** na experiência do usuário

## 📞 Suporte

- **Documentação:** Consulte o arquivo de plano completo
- **Padrões:** Siga o código existente no projeto
- **Componentes:** Use Shadcn UI como referência
- **API:** Consulte os endpoints existentes em `packages/api`

## ⚡ Dicas Importantes

1. **Priorize a UX:** A interface deve ser idêntica à imagem
2. **Mantenha consistência:** Use os padrões do projeto
3. **Teste constantemente:** Valide cada funcionalidade
4. **Documente mudanças:** Comente código complexo
5. **Seja responsivo:** Funcione em todos os dispositivos

---

**🎯 Meta Final:** Criar uma experiência de configuração de produtos moderna, intuitiva e funcional que replique exatamente a imagem de referência, integrando-se perfeitamente com o sistema existente.

**📁 Arquivo de Referência:** `PRODUCT_CONFIGURATION_UPGRADE_PLAN.md`

**🚀 Comece agora!** Implemente a Fase 1 do plano e crie a interface de configuração de produtos.
