{"name": "supgateway", "private": true, "scripts": {"build": "dotenv -c -- turbo build", "dev": "dotenv -c -- turbo dev --concurrency 15", "start": "dotenv -c -- turbo start", "lint": "biome lint .", "clean": "turbo clean", "format": "biome format . --write", "test:checkout:create": "pnpm --filter @repo/database test:checkout:create", "test:checkout:cleanup": "pnpm --filter @repo/database test:checkout:cleanup"}, "engines": {"node": ">=20"}, "packageManager": "pnpm@9.3.0", "devDependencies": {"@biomejs/biome": "1.9.4", "@repo/tsconfig": "workspace:*", "@types/node": "^22.15.30", "dotenv-cli": "^8.0.0", "turbo": "^2.5.4", "typescript": "5.8.3"}, "pnpm": {"overrides": {"@types/react": "19.0.0", "@types/react-dom": "19.0.0"}}}