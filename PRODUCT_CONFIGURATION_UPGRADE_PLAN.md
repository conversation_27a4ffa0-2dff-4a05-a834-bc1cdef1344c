# 🚀 Plano de Upgrade para Configuração de Produtos

Este documento detalha as etapas necessárias para refatorar e modernizar a experiência de criação e configuração de produtos, alinhando-a com o design e as funcionalidades apresentadas na imagem de referência.

## 📋 Análise da Imagem e Requisitos

A imagem de referência apresenta uma interface de usuário moderna e intuitiva para a configuração de um produto digital. Os principais elementos e funcionalidades a serem implementados são:

### 1. Layout Geral e Navegação
*   **Header:** Logo `KVN`, breadcrumbs (`Produtos > Plataforma Digit... > Configurações`), informações do usuário (`Pessoa Física`, `R$ 0,00 faturado`, `0 venda realizada`).
*   **Sidebar de Navegação:** Uma barra lateral esquerda com ícones e links para as seguintes seções:
    *   Ofertas
    *   Checkouts
    *   **Configurações** (página atual)
    *   Pixels de rastreamento
    *   Upsell, downsell e mais
    *   Cupons
    *   Afiliação
    *   Coprodução
*   **Área de Conteúdo Principal:** Dividida em seções claras para diferentes aspectos da configuração do produto.

### 2. Seções de Configuração Detalhadas

#### 2.1. Informações Básicas
*   **Imagem do Produto:** Área para upload/visualização de uma imagem representativa do produto (ilustração de smartphone com livros).
*   **Nome do Produto:** Campo de texto com limite de caracteres (ex: 60 caracteres), exibido em todos os locais da plataforma.
*   **Descrição do Produto:** Área de texto com limite de caracteres (ex: 200 caracteres), para uma breve descrição do produto.

#### 2.2. Suporte ao Comprador
*   **Nome do Vendedor:** Campo de texto para o nome do responsável pelo produto.
*   **E-mail:** Campo de texto para o e-mail de contato, com opção de "Atualizar".
*   **Telefone:** Campo de texto opcional para o telefone de contato, com opção de "Cadastrar" e nota sobre verificação.

#### 2.3. Preferências
*   **Categoria:** Dropdown para selecionar a categoria do produto (ex: "Tecnologia da Informação").
*   **Formato:** Dropdown para selecionar o formato do produto (ex: "Serviço").
*   **Idioma:** Dropdown para selecionar o idioma principal do produto (ex: "Português").
*   **Moeda Base:** Dropdown para selecionar a moeda padrão para vendas (ex: "Real (R$)").
*   **Página de Vendas:** Campo de texto para a URL da página de vendas do produto.

#### 2.4. Status
*   **Status Ativo/Inativo:** Um toggle switch para ativar ou desativar o produto para vendas, acompanhado de um rótulo "Ativo".

#### 2.5. Recuperação Ativa
*   **Funcionalidade:** Seção com descrição sobre a recuperação de vendas e um botão "Configurar".

### 3. Ações da Página
*   **Excluir Produto:** Botão de ação destrutiva (vermelho) para remover o produto.
*   **Salvar Alterações:** Botão para persistir as configurações do produto.

---

## 🛠️ Plano de Implementação (TODOs Detalhados)

### Fase 1: Análise e Design (Concluída/Em Progresso)

*   **[COMPLETED]** `analyze-current-structure`: Analisar estrutura atual de criação/configuração de produtos.
*   **[IN_PROGRESS]** `design-new-layout`: Projetar novo layout de configuração de produto baseado na imagem.

### Fase 2: Desenvolvimento Frontend

1.  **[PENDING]** `create-product-configuration-page`: Criar a página principal de configuração de produto (`/products/[productId]/configuracoes`).
    *   Implementar o layout base com header, sidebar e área de conteúdo principal.
    *   Garantir que o tema escuro e a tipografia da imagem sejam replicados.

2.  **[PENDING]** `create-sidebar-navigation`: Criar sidebar de navegação com as seções listadas (Ofertas, Checkouts, Configurações, Pixels, Upsell, Cupons, Afiliação, Coprodução).
    *   Implementar a lógica de destaque para a seção ativa ("Configurações").
    *   Garantir que os ícones e o estilo visual correspondam à imagem.

3.  **[PENDING]** `implement-basic-info-section`: Implementar a seção de "Informações Básicas".
    *   Componente de upload de imagem com preview (ilustração de smartphone com livros).
    *   Campo de input para `Nome do Produto` com contador de caracteres e helper text.
    *   Textarea para `Descrição do Produto` com contador de caracteres e helper text.

4.  **[PENDING]** `implement-buyer-support`: Implementar a seção de "Suporte ao Comprador".
    *   Campo de input para `Nome do Vendedor`.
    *   Campo de input para `E-mail` com valor atual e botão "Atualizar".
    *   Campo de input para `Telefone` (opcional) com helper text e botão "Cadastrar".

5.  **[PENDING]** `implement-preferences`: Implementar a seção de "Preferências".
    *   Dropdown para `Categoria` (ex: "Tecnologia da Informação").
    *   Dropdown para `Formato` (ex: "Serviço").
    *   Dropdown para `Idioma` (ex: "Português").
    *   Dropdown para `Moeda Base` (ex: "Real (R$)").
    *   Campo de input para `Página de Vendas` (URL).

6.  **[PENDING]** `implement-status-management`: Implementar o gerenciamento de status ativo/inativo do produto.
    *   Componente de toggle switch para `Ativo/Inativo`.
    *   Exibição do status atual (tag "Ativo" em verde).

7.  **[PENDING]** `implement-active-recovery`: Implementar a funcionalidade de "Recuperação Ativa".
    *   Seção com texto descritivo e um botão "Configurar".

8.  **[PENDING]** `implement-action-buttons`: Implementar os botões de ação na parte inferior da página.
    *   Botão "Excluir produto" (estilo destrutivo).
    *   Botão "Salvar alterações" (inicialmente desabilitado, habilitar ao detectar mudanças).

### Fase 3: Desenvolvimento Backend e Banco de Dados

1.  **[PENDING]** `update-database-schema`: Atualizar o schema do banco de dados para suportar os novos campos de configuração.

    *   **Tabela `products` (ou similar):**
        *   `product_image_url` (VARCHAR): URL da imagem do produto.
        *   `name` (VARCHAR): Nome do produto (já deve existir, mas verificar limite de 60 caracteres).
        *   `description` (TEXT): Descrição do produto (verificar limite de 200 caracteres).
        *   `seller_name` (VARCHAR): Nome do vendedor.
        *   `seller_email` (VARCHAR): E-mail do vendedor.
        *   `seller_phone` (VARCHAR, NULLABLE): Telefone do vendedor.
        *   `category_id` (INT, FK para `categories`): ID da categoria.
        *   `format_id` (INT, FK para `formats`): ID do formato (Serviço, Curso, Ebook, etc.).
        *   `language_code` (VARCHAR, FK para `languages`): Código do idioma (ex: 'pt-BR').
        *   `base_currency_code` (VARCHAR, FK para `currencies`): Código da moeda base (ex: 'BRL').
        *   `sales_page_url` (VARCHAR): URL da página de vendas.
        *   `is_active` (BOOLEAN): Status ativo/inativo do produto.
        *   `active_recovery_enabled` (BOOLEAN): Flag para indicar se a recuperação ativa está habilitada.
        *   `active_recovery_config` (JSONB, NULLABLE): Configurações específicas da recuperação ativa.

    *   **Novas Tabelas (se não existirem):**
        *   `categories`: `id`, `name`.
        *   `formats`: `id`, `name`.
        *   `languages`: `code`, `name`.
        *   `currencies`: `code`, `name`, `symbol`.

2.  **[PENDING]** `create-api-endpoints`: Criar ou atualizar endpoints da API para as novas funcionalidades.

    *   **GET `/api/products/{productId}/configurations`:**
        *   Retorna todos os dados de configuração de um produto específico.
    *   **PUT `/api/products/{productId}/configurations`:**
        *   Atualiza todos os dados de configuração de um produto.
        *   Payload: `{ name, description, product_image_url, seller_name, seller_email, seller_phone, category_id, format_id, language_code, base_currency_code, sales_page_url, is_active, active_recovery_enabled, active_recovery_config }`.
    *   **DELETE `/api/products/{productId}`:**
        *   Endpoint para excluir um produto.
    *   **POST `/api/products/{productId}/seller-phone/verify`:**
        *   Endpoint para iniciar o processo de verificação de telefone.
    *   **POST `/api/products/{productId}/seller-phone/register`:**
        *   Endpoint para registrar o telefone após a verificação.
    *   **POST `/api/products/{productId}/seller-email/update`:**
        *   Endpoint para atualizar o e-mail do vendedor.
    *   **GET `/api/categories`:**
        *   Retorna a lista de categorias para o dropdown.
    *   **GET `/api/formats`:**
        *   Retorna a lista de formatos para o dropdown.
    *   **GET `/api/languages`:**
        *   Retorna a lista de idiomas para o dropdown.
    *   **GET `/api/currencies`:**
        *   Retorna a lista de moedas para o dropdown.
    *   **POST `/api/products/{productId}/active-recovery/configure`:**
        *   Endpoint para configurar a recuperação ativa.

### Fase 4: Integração e Testes

1.  **[PENDING]** `integrate-with-existing-system`: Integrar o novo sistema de configuração com a estrutura existente de produtos.
    *   Garantir que as rotas e permissões estejam corretas.
    *   Conectar os componentes frontend aos novos endpoints da API.
    *   Atualizar quaisquer outros módulos que dependam das configurações do produto para usar os novos dados.

2.  **[PENDING]** `testing`: Realizar testes completos.
    *   Testes unitários para componentes e funções de backend.
    *   Testes de integração para a API e a comunicação frontend/backend.
    *   Testes de UI/UX para garantir que a experiência do usuário seja fluida e corresponda ao design.
    *   Testes de segurança e validação de dados.

---

## 📁 Estrutura de Arquivos a Ser Criada/Modificada

### Frontend (apps/web)
```
apps/web/
├── app/(saas)/app/products/
│   ├── [productId]/
│   │   └── configuracoes/
│   │       └── page.tsx                    # Página principal de configuração
│   └── components/
│       ├── ProductConfigurationLayout.tsx  # Layout principal com sidebar
│       ├── ProductSidebar.tsx              # Sidebar de navegação
│       ├── BasicInfoSection.tsx            # Seção de informações básicas
│       ├── BuyerSupportSection.tsx         # Seção de suporte ao comprador
│       ├── PreferencesSection.tsx          # Seção de preferências
│       ├── StatusSection.tsx               # Seção de status
│       ├── ActiveRecoverySection.tsx       # Seção de recuperação ativa
│       └── ProductImageUpload.tsx          # Componente de upload de imagem
```

### Backend (packages)
```
packages/
├── database/
│   └── prisma/
│       └── schema.prisma                   # Atualizar schema
├── api/
│   └── src/
│       └── routes/
│           ├── products/
│           │   └── configurations.ts       # Novos endpoints de configuração
│           ├── categories.ts               # Endpoints de categorias
│           ├── formats.ts                  # Endpoints de formatos
│           ├── languages.ts                # Endpoints de idiomas
│           └── currencies.ts               # Endpoints de moedas
```

---

## 🎨 Especificações de Design

### Cores e Tema
- **Tema:** Escuro (dark mode)
- **Cores principais:**
  - Background principal: `#1a1a1a` ou similar
  - Sidebar: `#2d2d2d` ou similar
  - Cards: `#3a3a3a` ou similar
  - Texto principal: `#ffffff`
  - Texto secundário: `#a0a0a0`

### Tipografia
- **Fonte principal:** Inter ou similar
- **Tamanhos:**
  - Título da página: `text-2xl` ou `text-3xl`
  - Subtítulos de seção: `text-lg` ou `text-xl`
  - Texto do corpo: `text-sm` ou `text-base`
  - Texto de ajuda: `text-xs`

### Componentes
- **Botões:**
  - Primário: Azul/verde com hover
  - Secundário: Cinza com hover
  - Destrutivo: Vermelho com hover
- **Inputs:** Bordas arredondadas, focus com outline colorido
- **Cards:** Sombra sutil, bordas arredondadas
- **Sidebar:** Ícones + texto, hover effects

---

## 🔧 Configurações Técnicas

### Validações
- **Nome do produto:** Máximo 60 caracteres, obrigatório
- **Descrição:** Máximo 200 caracteres, opcional
- **E-mail do vendedor:** Formato de e-mail válido
- **Telefone:** Formato brasileiro (opcional)
- **URL da página de vendas:** Formato de URL válido

### Estados da Interface
- **Loading:** Durante carregamento de dados
- **Saving:** Durante salvamento
- **Error:** Exibição de erros de validação
- **Success:** Confirmação de salvamento
- **Dirty:** Indicação de mudanças não salvas

### Responsividade
- **Mobile:** Sidebar colapsável, layout em coluna única
- **Tablet:** Sidebar fixa, layout adaptativo
- **Desktop:** Layout completo com sidebar fixa

---

## 📋 Checklist de Implementação

### Frontend
- [ ] Criar layout principal com header e sidebar
- [ ] Implementar todas as seções de configuração
- [ ] Adicionar validações de formulário
- [ ] Implementar upload de imagem
- [ ] Adicionar estados de loading/error/success
- [ ] Garantir responsividade
- [ ] Implementar navegação entre seções

### Backend
- [ ] Atualizar schema do banco de dados
- [ ] Criar migrações
- [ ] Implementar todos os endpoints da API
- [ ] Adicionar validações de dados
- [ ] Implementar lógica de negócio
- [ ] Adicionar logs e monitoramento

### Integração
- [ ] Conectar frontend com API
- [ ] Implementar gerenciamento de estado
- [ ] Adicionar tratamento de erros
- [ ] Implementar cache quando necessário
- [ ] Testar fluxo completo

### Testes
- [ ] Testes unitários para componentes
- [ ] Testes de integração para API
- [ ] Testes E2E para fluxo completo
- [ ] Testes de acessibilidade
- [ ] Testes de performance

---

## 🚀 Próximos Passos

1. **Iniciar com o Frontend:** Começar criando o layout principal e as seções básicas
2. **Implementar Backend:** Criar os endpoints necessários em paralelo
3. **Integração:** Conectar frontend e backend
4. **Testes:** Implementar testes abrangentes
5. **Deploy:** Fazer deploy em ambiente de desenvolvimento
6. **Feedback:** Coletar feedback e iterar

---

Este plano fornece uma base sólida para o desenvolvimento. O próximo passo é iniciar a implementação das tarefas de frontend e backend em paralelo, seguindo a ordem lógica e priorizando a experiência do usuário conforme a imagem de referência.
