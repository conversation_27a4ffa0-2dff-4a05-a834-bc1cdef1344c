import { getSession } from "@saas/auth/lib/server";
import { isAdmin } from "@repo/auth/lib/helper";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import { Button } from "@ui/components/button";
import { Badge } from "@ui/components/badge";
import { PlusIcon, SearchIcon, FilterIcon, EyeIcon, EditIcon, MoreHorizontalIcon, ImageIcon, UsersIcon, DollarSignIcon } from "lucide-react";
import { Input } from "@ui/components/input";
import { redirect } from "next/navigation";
import { db } from "@repo/database/prisma/client";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuTrigger,
} from "@ui/components/dropdown-menu";
import { ProductsClient } from "./components/products-client";
import Link from "next/link";

export default async function ProductsPage() {
	const session = await getSession();

	if (!session) {
		redirect("/auth/login");
	}

	// Verificar se é admin - admins podem acessar sem membership
	if (isAdmin(session.user)) {
		// Para admins, buscar a primeira organização disponível
		const organization = await db.organization.findFirst();

		if (!organization) {
			redirect("/auth/login");
		}

		return (
			<ProductsClient
				organizationId={organization.id}
				userId={session.user.id}
			/>
		);
	}

	// Buscar organização do usuário para usuários normais
	const userMembership = await db.member.findFirst({
		where: {
			userId: session.user.id,
		},
		include: {
			organization: true,
		},
	});

	if (!userMembership) {
		redirect("/auth/login");
	}

	return (
		<ProductsClient
			organizationId={userMembership.organization.id}
			userId={session.user.id}
		/>
	);
}
