"use client";

import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import { But<PERSON> } from "@ui/components/button";
import { Badge } from "@ui/components/badge";
import { PlusIcon, SearchIcon, FilterIcon, EyeIcon, EditIcon, MoreHorizontalIcon, ImageIcon, UsersIcon, DollarSignIcon } from "lucide-react";
import { Input } from "@ui/components/input";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuTrigger,
} from "@ui/components/dropdown-menu";
import { useProducts, useDeleteProduct, useUpdateProductStatus } from "@saas/products/hooks/useProductsApi";
import { Product } from "@saas/products/hooks/useProductsApi";
import Link from "next/link";

interface ProductsClientProps {
	organizationId: string;
	userId: string;
}

export function ProductsClient({ organizationId, userId }: ProductsClientProps) {
	const [search, setSearch] = useState("");
	const [statusFilter, setStatusFilter] = useState<string | undefined>();
	const [typeFilter, setTypeFilter] = useState<string | undefined>();
	const [page, setPage] = useState(1);

	const { data: productsData, isLoading, error } = useProducts(organizationId, {
		page,
		limit: 10,
		search: search || undefined,
		status: statusFilter as any,
		type: typeFilter as any,
	});

	const deleteProduct = useDeleteProduct();
	const updateStatus = useUpdateProductStatus();

	const products = productsData?.products || [];
	const pagination = productsData?.pagination;

	const getStatusBadgeVariant = (status: string) => {
		switch (status) {
			case 'PUBLISHED':
				return 'default';
			case 'DRAFT':
				return 'secondary';
			case 'ARCHIVED':
				return 'outline';
			case 'SUSPENDED':
				return 'destructive';
			default:
				return 'outline';
		}
	};

	const getStatusLabel = (status: string) => {
		switch (status) {
			case 'PUBLISHED':
				return 'Publicado';
			case 'DRAFT':
				return 'Rascunho';
			case 'ARCHIVED':
				return 'Arquivado';
			case 'SUSPENDED':
				return 'Suspenso';
			default:
				return status;
		}
	};

	const getTypeLabel = (type: string) => {
		switch (type) {
			case 'COURSE':
				return 'Curso';
			case 'EBOOK':
				return 'E-book';
			case 'MENTORING':
				return 'Mentoria';
			case 'SUBSCRIPTION':
				return 'Assinatura';
			case 'BUNDLE':
				return 'Pacote';
			default:
				return type;
		}
	};

	const formatCurrency = (cents: number, currency: string = 'BRL') => {
		return new Intl.NumberFormat('pt-BR', {
			style: 'currency',
			currency,
		}).format(cents / 100);
	};

	const handleDeleteProduct = async (productId: string) => {
		if (confirm("Tem certeza que deseja deletar este produto?")) {
			await deleteProduct.mutateAsync(productId);
		}
	};

	const handleUpdateStatus = async (productId: string, status: Product["status"]) => {
		await updateStatus.mutateAsync({ id: productId, status });
	};

	if (isLoading) {
		return (
			<div className="space-y-6">
				<div className="flex items-center justify-between">
					<div>
						<h1 className="text-3xl font-bold">Meus Produtos</h1>
						<p className="text-muted-foreground">
							Gerencie seus produtos digitais e acompanhe as vendas
						</p>
					</div>
				</div>
				<div className="grid gap-4 md:grid-cols-4">
					{[...Array(4)].map((_, i) => (
						<Card key={i}>
							<CardHeader className="animate-pulse">
								<div className="h-4 bg-gray-200 rounded w-3/4"></div>
							</CardHeader>
							<CardContent className="animate-pulse">
								<div className="h-8 bg-gray-200 rounded w-1/2"></div>
							</CardContent>
						</Card>
					))}
				</div>
			</div>
		);
	}

	if (error) {
		return (
			<div className="space-y-6">
				<div className="flex items-center justify-between">
					<div>
						<h1 className="text-3xl font-bold">Meus Produtos</h1>
						<p className="text-muted-foreground">
							Gerencie seus produtos digitais e acompanhe as vendas
						</p>
					</div>
				</div>
				<Card>
					<CardContent className="flex flex-col items-center justify-center py-12">
						<p className="text-red-500">Erro ao carregar produtos</p>
					</CardContent>
				</Card>
			</div>
		);
	}

	// Calcular estatísticas
	const totalProducts = pagination?.total || 0;
	const totalEnrollments = products.reduce((acc, product) => acc + (product._count?.enrollments || 0), 0);
	const totalSales = products.reduce((acc, product) => acc + (product._count?.orders || 0), 0);
	const totalRevenue = products.reduce((acc, product) => {
		// Aqui você precisaria buscar os dados de receita de cada produto
		// Por enquanto, vamos usar um valor estimado baseado nas vendas
		return acc + ((product._count?.orders || 0) * product.priceCents);
	}, 0);

	return (
		<div className="space-y-6">
			<div className="flex items-center justify-between">
				<div>
					<h1 className="text-3xl font-bold">Meus Produtos</h1>
					<p className="text-muted-foreground">
						Gerencie seus produtos digitais e acompanhe as vendas
					</p>
				</div>
				<Link href="/app/products/new">
					<Button>
						<PlusIcon className="h-4 w-4 mr-2" />
						Novo Produto
					</Button>
				</Link>
			</div>

			<div className="flex items-center gap-4">
				<div className="relative flex-1 max-w-sm">
					<SearchIcon className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
					<Input
						placeholder="Buscar produtos..."
						className="pl-10"
						value={search}
						onChange={(e) => setSearch(e.target.value)}
					/>
				</div>
				<Button variant="outline">
					<FilterIcon className="mr-2 h-4 w-4" />
					Filtros
				</Button>
			</div>

			{/* Stats Cards */}
			<div className="grid gap-4 md:grid-cols-4">
				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">Total de Produtos</CardTitle>
						<ImageIcon className="h-4 w-4 text-muted-foreground" />
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold">{totalProducts}</div>
					</CardContent>
				</Card>
				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">Alunos</CardTitle>
						<UsersIcon className="h-4 w-4 text-muted-foreground" />
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold">{totalEnrollments}</div>
					</CardContent>
				</Card>
				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">Vendas</CardTitle>
						<DollarSignIcon className="h-4 w-4 text-muted-foreground" />
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold">{totalSales}</div>
					</CardContent>
				</Card>
				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">Receita Total</CardTitle>
						<DollarSignIcon className="h-4 w-4 text-muted-foreground" />
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold">
							{formatCurrency(totalRevenue)}
						</div>
					</CardContent>
				</Card>
			</div>

			{/* Products Grid */}
			<div className="grid gap-4">
				{products.map((product) => (
					<Card key={product.id}>
						<CardHeader>
							<div className="flex items-center justify-between">
								<div className="flex items-center space-x-4">
									{product.thumbnail ? (
										<img
											src={product.thumbnail}
											alt={product.name}
											className="w-16 h-16 rounded-lg object-cover"
										/>
									) : (
										<div className="w-16 h-16 rounded-lg bg-gray-100 flex items-center justify-center">
											<ImageIcon className="h-8 w-8 text-gray-400" />
										</div>
									)}
									<div>
										<CardTitle className="text-lg">{product.name}</CardTitle>
										<CardDescription>
											{getTypeLabel(product.type)} • {product.category?.name || 'Sem categoria'}
										</CardDescription>
									</div>
								</div>
								<div className="flex items-center gap-2">
									<Badge variant={getStatusBadgeVariant(product.status)}>
										{getStatusLabel(product.status)}
									</Badge>
									<Badge variant="outline">
										{formatCurrency(product.priceCents, product.currency)}
									</Badge>
									<DropdownMenu>
										<DropdownMenuTrigger asChild>
											<Button variant="outline" size="sm">
												<MoreHorizontalIcon className="h-4 w-4" />
											</Button>
										</DropdownMenuTrigger>
										<DropdownMenuContent align="end">
											<DropdownMenuItem>
												<EyeIcon className="mr-2 h-4 w-4" />
												Visualizar
											</DropdownMenuItem>
											<DropdownMenuItem>
												<EditIcon className="mr-2 h-4 w-4" />
												Editar
											</DropdownMenuItem>
											<DropdownMenuItem>Duplicar</DropdownMenuItem>
											<DropdownMenuItem>Analytics</DropdownMenuItem>
											{product.status === 'PUBLISHED' ? (
												<DropdownMenuItem
													onClick={() => handleUpdateStatus(product.id, 'DRAFT')}
												>
													Despublicar
												</DropdownMenuItem>
											) : (
												<DropdownMenuItem
													onClick={() => handleUpdateStatus(product.id, 'PUBLISHED')}
												>
													Publicar
												</DropdownMenuItem>
											)}
											<DropdownMenuItem
												className="text-destructive"
												onClick={() => handleDeleteProduct(product.id)}
											>
												Deletar
											</DropdownMenuItem>
										</DropdownMenuContent>
									</DropdownMenu>
								</div>
							</div>
						</CardHeader>
						<CardContent>
							<div className="grid grid-cols-3 gap-4">
								<div className="flex items-center space-x-2">
									<UsersIcon className="h-4 w-4 text-muted-foreground" />
									<div>
										<p className="text-sm font-medium">Alunos</p>
										<p className="text-xs text-muted-foreground">
											{product._count?.enrollments || 0}
										</p>
									</div>
								</div>
								<div className="flex items-center space-x-2">
									<DollarSignIcon className="h-4 w-4 text-muted-foreground" />
									<div>
										<p className="text-sm font-medium">Vendas</p>
										<p className="text-xs text-muted-foreground">
											{product._count?.orders || 0}
										</p>
									</div>
								</div>
								<div className="flex items-center space-x-2">
									<EyeIcon className="h-4 w-4 text-muted-foreground" />
									<div>
										<p className="text-sm font-medium">Visibilidade</p>
										<p className="text-xs text-muted-foreground">
											{product.visibility === 'PUBLIC' ? 'Público' : 'Privado'}
										</p>
									</div>
								</div>
							</div>
						</CardContent>
					</Card>
				))}
			</div>

			{/* Pagination */}
			{pagination && pagination.pages > 1 && (
				<div className="flex items-center justify-center space-x-2">
					<Button
						variant="outline"
						onClick={() => setPage(page - 1)}
						disabled={page === 1}
					>
						Anterior
					</Button>
					<span className="text-sm text-muted-foreground">
						Página {page} de {pagination.pages}
					</span>
					<Button
						variant="outline"
						onClick={() => setPage(page + 1)}
						disabled={page === pagination.pages}
					>
						Próxima
					</Button>
				</div>
			)}

			{products.length === 0 && (
				<Card>
					<CardContent className="flex flex-col items-center justify-center py-12">
						<ImageIcon className="h-12 w-12 text-gray-400 mb-4" />
						<h3 className="text-lg font-medium text-gray-900 mb-2">
							Nenhum produto encontrado
						</h3>
						<p className="text-gray-500 text-center mb-6">
							Crie seu primeiro produto digital e comece a vender online
						</p>
						<Link href="/app/products/new">
							<Button>
								<PlusIcon className="h-4 w-4 mr-2" />
								Criar Primeiro Produto
							</Button>
						</Link>
					</CardContent>
				</Card>
			)}
		</div>
	);
}
