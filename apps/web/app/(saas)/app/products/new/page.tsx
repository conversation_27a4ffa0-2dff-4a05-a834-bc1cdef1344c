import { getSession } from "@saas/auth/lib/server";
import { isAdmin } from "@repo/auth/lib/helper";
import { redirect } from "next/navigation";
import { db } from "@repo/database/prisma/client";
import { CreateProductForm } from "./components/CreateProductForm";
import {
	Breadcrumb,
	BreadcrumbItem,
	BreadcrumbLink,
	BreadcrumbList,
	BreadcrumbPage,
	BreadcrumbSeparator
} from "@ui/components/breadcrumb";
import Link from "next/link";

export default async function NewProductPage() {
	const session = await getSession();

	if (!session) {
		redirect("/auth/login");
	}

	let organization;

	// Verificar se é admin - admins podem acessar sem membership
	if (isAdmin(session.user)) {
		// Para admins, buscar a primeira organização disponível
		organization = await db.organization.findFirst();

		if (!organization) {
			redirect("/auth/login");
		}
	} else {
		// Buscar organização do usuário para usuários normais
		const userMembership = await db.member.findFirst({
			where: {
				userId: session.user.id,
			},
			include: {
				organization: true,
			},
		});

		if (!userMembership) {
			redirect("/auth/login");
		}

		organization = userMembership.organization;
	}

	return (
		<div className="space-y-6">
			{/* Breadcrumbs */}
			<Breadcrumb>
				<BreadcrumbList>
					<BreadcrumbItem>
						<BreadcrumbLink asChild>
							<Link href="/app/products">Produtos</Link>
						</BreadcrumbLink>
					</BreadcrumbItem>
					<BreadcrumbSeparator />
					<BreadcrumbItem>
						<BreadcrumbPage>Criar Produto</BreadcrumbPage>
					</BreadcrumbItem>
				</BreadcrumbList>
			</Breadcrumb>

			<div className="flex items-center justify-between">
				<div>
					<h1 className="text-3xl font-bold">Criar Novo Produto</h1>
					<p className="text-muted-foreground">
						Preencha as informações básicas para criar seu produto. Após a criação, você poderá configurar detalhes avançados.
					</p>
				</div>
			</div>

			<CreateProductForm organization={organization} />
		</div>
	);
}
