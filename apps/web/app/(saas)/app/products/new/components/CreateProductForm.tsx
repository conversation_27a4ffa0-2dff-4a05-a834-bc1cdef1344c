"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { toast } from "sonner";
import { <PERSON><PERSON> } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { Label } from "@ui/components/label";
import { Textarea } from "@ui/components/textarea";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@ui/components/select";
import { Switch } from "@ui/components/switch";
import { useCreateProduct, CreateProductData } from "@saas/products/hooks/useProductsApi";

const createProductSchema = z.object({
  name: z.string().min(1, "Nome é obrigatório"),
  slug: z.string().min(1, "Slug é obrigatório").regex(/^[a-z0-9-]+$/, "Slug deve conter apenas letras minúsculas, números e hífens"),
  description: z.string().optional(),
  shortDescription: z.string().optional(),
  priceCents: z.number().min(0, "Preço deve ser maior ou igual a 0"),
  comparePriceCents: z.number().min(0).optional(),
  currency: z.string().default("BRL"),
  type: z.enum(["COURSE", "EBOOK", "MENTORSHIP", "SUBSCRIPTION", "BUNDLE"]),
  status: z.enum(["DRAFT", "PUBLISHED", "ARCHIVED", "SUSPENDED"]).default("DRAFT"),
  visibility: z.enum(["PUBLIC", "PRIVATE", "UNLISTED"]).default("PRIVATE"),
  categoryId: z.string().optional(),
  thumbnail: z.string().optional(),
  gallery: z.array(z.string()).default([]),
  tags: z.array(z.string()).default([]),
  features: z.array(z.string()).default([]),
  requirements: z.array(z.string()).default([]),
  duration: z.number().min(0).optional(),
  level: z.string().optional(),
  language: z.string().default("pt-BR"),
  certificate: z.boolean().default(false),
  downloadable: z.boolean().default(false),
  checkoutType: z.enum(["DEFAULT", "CUSTOM", "EXTERNAL"]).default("DEFAULT"),
  settings: z.record(z.any()).default({}),
});

type CreateProductFormData = z.infer<typeof createProductSchema>;

interface CreateProductFormProps {
  organization: any;
}

export function CreateProductForm({ organization }: CreateProductFormProps) {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [tags, setTags] = useState<string[]>([]);
  const [features, setFeatures] = useState<string[]>([]);
  const [requirements, setRequirements] = useState<string[]>([]);
  const [newTag, setNewTag] = useState("");
  const [newFeature, setNewFeature] = useState("");
  const [newRequirement, setNewRequirement] = useState("");

  const createProductMutation = useCreateProduct();

  const form = useForm<CreateProductFormData>({
    resolver: zodResolver(createProductSchema),
    defaultValues: {
      currency: "BRL",
      status: "DRAFT",
      visibility: "PRIVATE",
      type: "COURSE",
      language: "pt-BR",
      certificate: false,
      downloadable: false,
      checkoutType: "DEFAULT",
    },
  });

  const onSubmit = async (data: CreateProductFormData) => {
    try {
      setLoading(true);

      const productData: CreateProductData = {
        organizationId: organization.id,
        ...data,
        tags,
        features,
        requirements,
        settings: {},
      };

      const product = await createProductMutation.mutateAsync(productData);

      toast.success("Produto criado com sucesso! Redirecionando para configurações...");
      // Redirecionar para a página de configurações do produto
      setTimeout(() => {
        router.push(`/app/products/${product.id}/configuracoes`);
      }, 1000);
    } catch (error) {
      console.error("Error creating product:", error);
      // O toast de erro já é mostrado pelo hook
    } finally {
      setLoading(false);
    }
  };

  const addTag = () => {
    if (newTag.trim() && !tags.includes(newTag.trim())) {
      setTags([...tags, newTag.trim()]);
      setNewTag("");
    }
  };

  const removeTag = (tagToRemove: string) => {
    setTags(tags.filter(tag => tag !== tagToRemove));
  };

  const addFeature = () => {
    if (newFeature.trim() && !features.includes(newFeature.trim())) {
      setFeatures([...features, newFeature.trim()]);
      setNewFeature("");
    }
  };

  const removeFeature = (featureToRemove: string) => {
    setFeatures(features.filter(feature => feature !== featureToRemove));
  };

  const addRequirement = () => {
    if (newRequirement.trim() && !requirements.includes(newRequirement.trim())) {
      setRequirements([...requirements, newRequirement.trim()]);
      setNewRequirement("");
    }
  };

  const removeRequirement = (requirementToRemove: string) => {
    setRequirements(requirements.filter(requirement => requirement !== requirementToRemove));
  };

  return (
    <div className="space-y-6">
      {/* Informação sobre o fluxo */}
      <div className="bg-blue-50 dark:bg-blue-950/50 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
        <div className="flex items-start gap-3">
          <div className="w-5 h-5 rounded-full bg-blue-500 flex items-center justify-center flex-shrink-0 mt-0.5">
            <span className="text-white text-xs font-bold">i</span>
          </div>
          <div>
            <h4 className="font-medium text-blue-900 dark:text-blue-100 mb-1">
              Processo de Criação em 2 Etapas
            </h4>
            <p className="text-sm text-blue-700 dark:text-blue-300">
              Primeiro, preencha as informações básicas do produto. Após a criação, você será redirecionado para a página de configurações onde poderá definir detalhes avançados como suporte ao comprador, preferências e recuperação ativa.
            </p>
          </div>
        </div>
      </div>

      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
      <div className="grid gap-6 md:grid-cols-2">
        {/* Informações Básicas */}
        <Card>
          <CardHeader>
            <CardTitle>Informações Básicas</CardTitle>
            <CardDescription>
              Informações principais do produto
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="name">Nome do Produto *</Label>
              <Input
                id="name"
                placeholder="Ex: Curso de Marketing Digital"
                {...form.register("name")}
              />
              {form.formState.errors.name && (
                <p className="text-sm text-destructive">{form.formState.errors.name.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="slug">Slug *</Label>
              <Input
                id="slug"
                placeholder="Ex: curso-marketing-digital"
                {...form.register("slug")}
              />
              {form.formState.errors.slug && (
                <p className="text-sm text-destructive">{form.formState.errors.slug.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="type">Tipo de Produto *</Label>
              <Select onValueChange={(value) => form.setValue("type", value as any)} defaultValue={form.getValues("type")}>
                <SelectTrigger>
                  <SelectValue placeholder="Selecione o tipo" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="COURSE">Curso</SelectItem>
                  <SelectItem value="EBOOK">E-book</SelectItem>
                  <SelectItem value="MENTORSHIP">Mentoria</SelectItem>
                  <SelectItem value="SUBSCRIPTION">Assinatura</SelectItem>
                  <SelectItem value="BUNDLE">Pacote</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="shortDescription">Descrição Curta</Label>
              <Textarea
                id="shortDescription"
                placeholder="Descrição breve do produto"
                {...form.register("shortDescription")}
                rows={3}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Descrição Completa</Label>
              <Textarea
                id="description"
                placeholder="Descrição detalhada do produto"
                {...form.register("description")}
                rows={5}
              />
            </div>
          </CardContent>
        </Card>

        {/* Preços e Configurações */}
        <Card>
          <CardHeader>
            <CardTitle>Preços e Configurações</CardTitle>
            <CardDescription>
              Configure preços e configurações de venda
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="priceCents">Preço (em centavos) *</Label>
              <Input
                id="priceCents"
                type="number"
                placeholder="Ex: 29700 (R$ 297,00)"
                {...form.register("priceCents", { valueAsNumber: true })}
              />
              {form.formState.errors.priceCents && (
                <p className="text-sm text-destructive">{form.formState.errors.priceCents.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="comparePriceCents">Preço de Comparação (em centavos)</Label>
              <Input
                id="comparePriceCents"
                type="number"
                placeholder="Ex: 39700 (R$ 397,00)"
                {...form.register("comparePriceCents", { valueAsNumber: true })}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="currency">Moeda</Label>
              <Select onValueChange={(value) => form.setValue("currency", value)} defaultValue={form.getValues("currency")}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="BRL">Real (BRL)</SelectItem>
                  <SelectItem value="USD">Dólar (USD)</SelectItem>
                  <SelectItem value="EUR">Euro (EUR)</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="status">Status</Label>
              <Select onValueChange={(value) => form.setValue("status", value as any)} defaultValue={form.getValues("status")}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="DRAFT">Rascunho</SelectItem>
                  <SelectItem value="PUBLISHED">Publicado</SelectItem>
                  <SelectItem value="ARCHIVED">Arquivado</SelectItem>
                  <SelectItem value="SUSPENDED">Suspenso</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="visibility">Visibilidade</Label>
              <Select onValueChange={(value) => form.setValue("visibility", value as any)} defaultValue={form.getValues("visibility")}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="PUBLIC">Público</SelectItem>
                  <SelectItem value="PRIVATE">Privado</SelectItem>
                  <SelectItem value="UNLISTED">Não Listado</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Configurações Avançadas */}
      <Card>
        <CardHeader>
          <CardTitle>Configurações Avançadas</CardTitle>
          <CardDescription>
            Configurações específicas do tipo de produto
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2">
            <div className="space-y-2">
              <Label htmlFor="duration">Duração (em minutos)</Label>
              <Input
                id="duration"
                type="number"
                placeholder="Ex: 180 (3 horas)"
                {...form.register("duration", { valueAsNumber: true })}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="level">Nível</Label>
              <Select onValueChange={(value) => form.setValue("level", value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Selecione o nível" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="BEGINNER">Iniciante</SelectItem>
                  <SelectItem value="INTERMEDIATE">Intermediário</SelectItem>
                  <SelectItem value="ADVANCED">Avançado</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="language">Idioma</Label>
              <Select onValueChange={(value) => form.setValue("language", value)} defaultValue={form.getValues("language")}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="pt-BR">Português (Brasil)</SelectItem>
                  <SelectItem value="en-US">English (US)</SelectItem>
                  <SelectItem value="es-ES">Español</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="checkoutType">Tipo de Checkout</Label>
              <Select onValueChange={(value) => form.setValue("checkoutType", value as any)} defaultValue={form.getValues("checkoutType")}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="DEFAULT">Padrão</SelectItem>
                  <SelectItem value="CUSTOM">Personalizado</SelectItem>
                  <SelectItem value="EXTERNAL">Externo</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <Switch
              id="certificate"
              checked={form.watch("certificate")}
              onCheckedChange={(checked) => form.setValue("certificate", checked)}
            />
            <Label htmlFor="certificate">Oferece certificado</Label>
          </div>

          <div className="flex items-center space-x-2">
            <Switch
              id="downloadable"
              checked={form.watch("downloadable")}
              onCheckedChange={(checked) => form.setValue("downloadable", checked)}
            />
            <Label htmlFor="downloadable">Produto baixável</Label>
          </div>
        </CardContent>
      </Card>

      {/* Tags e Características */}
      <Card>
        <CardHeader>
          <CardTitle>Tags e Características</CardTitle>
          <CardDescription>
            Adicione tags, características e requisitos ao produto
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Tags */}
          <div className="space-y-2">
            <Label>Tags</Label>
            <div className="flex gap-2">
              <Input
                placeholder="Adicionar tag"
                value={newTag}
                onChange={(e) => setNewTag(e.target.value)}
                onKeyPress={(e) => e.key === "Enter" && (e.preventDefault(), addTag())}
              />
              <Button type="button" onClick={addTag} variant="outline">
                Adicionar
              </Button>
            </div>
            {tags.length > 0 && (
              <div className="flex flex-wrap gap-2 mt-2">
                {tags.map((tag, index) => (
                  <span
                    key={index}
                    className="px-2 py-1 bg-blue-100 text-blue-800 text-sm rounded-full flex items-center gap-1 dark:bg-blue-900 dark:text-blue-200"
                  >
                    {tag}
                    <button
                      type="button"
                      onClick={() => removeTag(tag)}
                      className="text-blue-600 hover:text-blue-800 dark:text-blue-300 dark:hover:text-blue-100"
                    >
                      ×
                    </button>
                  </span>
                ))}
              </div>
            )}
          </div>

          {/* Características */}
          <div className="space-y-2">
            <Label>Características</Label>
            <div className="flex gap-2">
              <Input
                placeholder="Adicionar característica"
                value={newFeature}
                onChange={(e) => setNewFeature(e.target.value)}
                onKeyPress={(e) => e.key === "Enter" && (e.preventDefault(), addFeature())}
              />
              <Button type="button" onClick={addFeature} variant="outline">
                Adicionar
              </Button>
            </div>
            {features.length > 0 && (
              <div className="flex flex-wrap gap-2 mt-2">
                {features.map((feature, index) => (
                  <span
                    key={index}
                    className="px-2 py-1 bg-green-100 text-green-800 text-sm rounded-full flex items-center gap-1 dark:bg-green-900 dark:text-green-200"
                  >
                    {feature}
                    <button
                      type="button"
                      onClick={() => removeFeature(feature)}
                      className="text-green-600 hover:text-green-800 dark:text-green-300 dark:hover:text-green-100"
                    >
                      ×
                    </button>
                  </span>
                ))}
              </div>
            )}
          </div>

          {/* Requisitos */}
          <div className="space-y-2">
            <Label>Requisitos</Label>
            <div className="flex gap-2">
              <Input
                placeholder="Adicionar requisito"
                value={newRequirement}
                onChange={(e) => setNewRequirement(e.target.value)}
                onKeyPress={(e) => e.key === "Enter" && (e.preventDefault(), addRequirement())}
              />
              <Button type="button" onClick={addRequirement} variant="outline">
                Adicionar
              </Button>
            </div>
            {requirements.length > 0 && (
              <div className="flex flex-wrap gap-2 mt-2">
                {requirements.map((requirement, index) => (
                  <span
                    key={index}
                    className="px-2 py-1 bg-orange-100 text-orange-800 text-sm rounded-full flex items-center gap-1 dark:bg-orange-900 dark:text-orange-200"
                  >
                    {requirement}
                    <button
                      type="button"
                      onClick={() => removeRequirement(requirement)}
                      className="text-orange-600 hover:text-orange-800 dark:text-orange-300 dark:hover:text-orange-100"
                    >
                      ×
                    </button>
                  </span>
                ))}
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Botões de Ação */}
      <div className="flex justify-end gap-4">
        <Button
          type="button"
          variant="outline"
          onClick={() => router.back()}
          disabled={loading}
        >
          Cancelar
        </Button>
        <Button type="submit" disabled={loading}>
          {loading ? "Criando..." : "Criar Produto"}
        </Button>
      </div>
    </form>
    </div>
  );
}
