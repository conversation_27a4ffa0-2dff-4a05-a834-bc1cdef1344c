import { getActiveOrganization } from "@saas/auth/lib/server";
import { notFound } from "next/navigation";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components";
import { Badge } from "@ui/components";
import { But<PERSON> } from "@ui/components";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@ui/components";
import {
  BarChart3,
  TrendingUp,
  Users,
  CreditCard,
  DollarSign,
  Activity,
  Calendar,
  Filter,
  Download,
  Eye,
  EyeOff
} from "lucide-react";

export default async function AnalyticsPage({
  params,
}: {
  params: Promise<{ organizationSlug: string }>;
}) {
  const { organizationSlug } = await params;
  const organization = await getActiveOrganization(organizationSlug);

  if (!organization) {
    return notFound();
  }

  // Mock data - in real app this would come from API
  const metrics = [
    {
      title: "Receita Total",
      value: "R$ 47.250,00",
      change: "+23.5%",
      isPositive: true,
      icon: DollarSign,
    },
    {
      title: "Transações",
      value: "1.247",
      change: "+18.2%",
      isPositive: true,
      icon: CreditCard,
    },
    {
      title: "Usuários Ativos",
      value: "892",
      change: "+12.8%",
      isPositive: true,
      icon: Users,
    },
    {
      title: "Taxa de Conversão",
      value: "3.2%",
      change: "+0.8%",
      isPositive: true,
      icon: TrendingUp,
    },
  ];

  const chartData = [
    { month: "Jan", revenue: 3200, transactions: 45 },
    { month: "Fev", revenue: 4100, transactions: 58 },
    { month: "Mar", revenue: 3800, transactions: 52 },
    { month: "Abr", revenue: 5200, transactions: 78 },
    { month: "Mai", revenue: 6800, transactions: 95 },
    { month: "Jun", revenue: 7500, transactions: 108 },
    { month: "Jul", revenue: 8200, transactions: 125 },
    { month: "Ago", revenue: 9500, transactions: 142 },
  ];

  const topProducts = [
    { name: "Curso de Marketing Digital", revenue: "R$ 18.750,00", sales: 125 },
    { name: "E-book: Guia Completo", revenue: "R$ 12.400,00", sales: 248 },
    { name: "Consultoria Premium", revenue: "R$ 8.900,00", sales: 45 },
    { name: "Template de Landing Page", revenue: "R$ 4.200,00", sales: 84 },
    { name: "Webinar Exclusivo", revenue: "R$ 3.000,00", sales: 60 },
  ];

  const topPaymentMethods = [
    { method: "PIX", percentage: 65, count: 810 },
    { method: "Cartão de Crédito", percentage: 28, count: 349 },
    { method: "Boleto", percentage: 7, count: 88 },
  ];

  return (
    <div className="space-y-6 p-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Analytics PIX</h1>
          <p className="text-muted-foreground">
            Métricas e insights detalhados dos pagamentos PIX da sua organização
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm">
            <Filter className="h-4 w-4 mr-2" />
            Filtros
          </Button>
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Exportar
          </Button>
        </div>
      </div>

      {/* Date Range */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm font-medium">Período:</span>
              <span className="text-sm text-muted-foreground">7/29/2025 - 8/28/2025</span>
            </div>
            <Badge status="info">Últimos 30 dias</Badge>
          </div>
        </CardContent>
      </Card>

      {/* Metrics Grid */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {metrics.map((metric, index) => (
          <Card key={index}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                {metric.title}
              </CardTitle>
              <metric.icon className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{metric.value}</div>
              <p className="text-xs text-muted-foreground">
                <span className={metric.isPositive ? "text-green-600" : "text-red-600"}>
                  {metric.change}
                </span>{" "}
                vs período anterior
              </p>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Main Content Tabs */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Visão Geral</TabsTrigger>
          <TabsTrigger value="revenue">Receita</TabsTrigger>
          <TabsTrigger value="transactions">Transações</TabsTrigger>
          <TabsTrigger value="products">Produtos</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            {/* Revenue Chart */}
            <Card>
              <CardHeader>
                <CardTitle>Receita Mensal</CardTitle>
                <CardDescription>
                  Evolução da receita ao longo dos meses
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-64 flex items-end justify-between gap-1">
                  {chartData.map((item, index) => (
                    <div key={index} className="flex-1 flex flex-col items-center">
                      <div
                        className="w-full bg-primary/20 rounded-t-sm transition-all duration-300"
                        style={{
                          height: `${Math.max(item.revenue / 100, 2)}px`,
                          minHeight: '2px'
                        }}
                      />
                      <span className="text-xs text-muted-foreground mt-1">
                        {item.month}
                      </span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Transactions Chart */}
            <Card>
              <CardHeader>
                <CardTitle>Transações Mensais</CardTitle>
                <CardDescription>
                  Volume de transações por mês
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-64 flex items-end justify-between gap-1">
                  {chartData.map((item, index) => (
                    <div key={index} className="flex-1 flex flex-col items-center">
                      <div
                        className="w-full bg-blue-500/20 rounded-t-sm transition-all duration-300"
                        style={{
                          height: `${Math.max(item.transactions / 10, 2)}px`,
                          minHeight: '2px'
                        }}
                      />
                      <span className="text-xs text-muted-foreground mt-1">
                        {item.month}
                      </span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="revenue" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            {/* Revenue by Payment Method */}
            <Card>
              <CardHeader>
                <CardTitle>Receita por Método de Pagamento</CardTitle>
                <CardDescription>
                  Distribuição da receita por forma de pagamento
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                      <span className="text-sm font-medium">PIX</span>
                    </div>
                    <div className="text-right">
                      <p className="font-semibold">R$ 30.712,50</p>
                      <p className="text-xs text-muted-foreground">65%</p>
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                      <span className="text-sm font-medium">Cartão de Crédito</span>
                    </div>
                    <div className="text-right">
                      <p className="font-semibold">R$ 13.230,00</p>
                      <p className="text-xs text-muted-foreground">28%</p>
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 bg-orange-500 rounded-full"></div>
                      <span className="text-sm font-medium">Boleto</span>
                    </div>
                    <div className="text-right">
                      <p className="font-semibold">R$ 3.307,50</p>
                      <p className="text-xs text-muted-foreground">7%</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Revenue Trend */}
            <Card>
              <CardHeader>
                <CardTitle>Tendência de Receita</CardTitle>
                <CardDescription>
                  Crescimento mensal da receita
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-64 flex items-end justify-between gap-1">
                  {chartData.map((item, index) => (
                    <div key={index} className="flex-1 flex flex-col items-center">
                      <div
                        className="w-full bg-gradient-to-t from-green-500 to-green-400 rounded-t-sm transition-all duration-300"
                        style={{
                          height: `${Math.max(item.revenue / 100, 8)}px`,
                          minHeight: '8px'
                        }}
                      />
                      <span className="text-xs text-muted-foreground mt-1">
                        {item.month}
                      </span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="transactions" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            {/* Recent Transactions */}
            <Card>
              <CardHeader>
                <CardTitle>Transações Recentes</CardTitle>
                <CardDescription>
                  Últimas transações processadas
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {[
                    { id: "TXN-001", customer: "João Silva", amount: "R$ 297,00", method: "PIX", status: "Pago", date: "Hoje" },
                    { id: "TXN-002", customer: "Maria Santos", amount: "R$ 150,00", method: "Cartão", status: "Pago", date: "Hoje" },
                    { id: "TXN-003", customer: "Pedro Costa", amount: "R$ 89,90", method: "PIX", status: "Pago", date: "Ontem" },
                    { id: "TXN-004", customer: "Ana Oliveira", amount: "R$ 450,00", method: "Boleto", status: "Pendente", date: "Ontem" },
                    { id: "TXN-005", customer: "Carlos Lima", amount: "R$ 199,00", method: "PIX", status: "Pago", date: "2 dias atrás" },
                  ].map((transaction, index) => (
                    <div key={index} className="flex items-center justify-between p-3 rounded-lg border bg-muted/50">
                      <div className="flex-1">
                        <div className="flex items-center gap-2">
                          <p className="font-medium text-sm">{transaction.customer}</p>
                          <Badge status={transaction.status === "Pago" ? "success" : "warning"}>
                            {transaction.status}
                          </Badge>
                        </div>
                        <p className="text-xs text-muted-foreground">
                          {transaction.id} • {transaction.method} • {transaction.date}
                        </p>
                      </div>
                      <div className="text-right">
                        <p className="font-semibold">{transaction.amount}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Transaction Status Distribution */}
            <Card>
              <CardHeader>
                <CardTitle>Status das Transações</CardTitle>
                <CardDescription>
                  Distribuição por status de pagamento
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                      <span className="text-sm font-medium">Pagas</span>
                    </div>
                    <div className="text-right">
                      <p className="font-semibold">1.089</p>
                      <p className="text-xs text-muted-foreground">87.3%</p>
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                      <span className="text-sm font-medium">Pendentes</span>
                    </div>
                    <div className="text-right">
                      <p className="font-semibold">98</p>
                      <p className="text-xs text-muted-foreground">7.9%</p>
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                      <span className="text-sm font-medium">Canceladas</span>
                    </div>
                    <div className="text-right">
                      <p className="font-semibold">60</p>
                      <p className="text-xs text-muted-foreground">4.8%</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="products" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            {/* Top Products */}
            <Card>
              <CardHeader>
                <CardTitle>Produtos Mais Vendidos</CardTitle>
                <CardDescription>
                  Ranking dos produtos com melhor performance
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {topProducts.map((product, index) => (
                    <div key={index} className="flex items-center justify-between p-3 rounded-lg border bg-muted/50">
                      <div>
                        <p className="font-medium">{product.name}</p>
                        <p className="text-sm text-muted-foreground">
                          {product.sales} vendas
                        </p>
                      </div>
                      <div className="text-right">
                        <p className="font-semibold">{product.revenue}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Payment Methods Distribution */}
            <Card>
              <CardHeader>
                <CardTitle>Métodos de Pagamento</CardTitle>
                <CardDescription>
                  Distribuição por método de pagamento
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {topPaymentMethods.map((method, index) => (
                    <div key={index} className="flex items-center justify-between">
                      <span className="text-sm font-medium">{method.method}</span>
                      <div className="flex items-center gap-2">
                        <div className="w-20 bg-muted rounded-full h-2">
                          <div
                            className={`h-2 rounded-full transition-all duration-300 ${
                              method.method === "PIX" ? "bg-green-500" :
                              method.method === "Cartão de Crédito" ? "bg-blue-500" :
                              "bg-orange-500"
                            }`}
                            style={{width: `${method.percentage}%`}}
                          />
                        </div>
                        <span className="text-sm text-muted-foreground w-12 text-right">
                          {method.percentage}%
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Ações Rápidas</CardTitle>
          <CardDescription>
            Acesse rapidamente as funcionalidades mais usadas
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-2">
            <Button variant="outline" size="sm">
              <Activity className="h-4 w-4 mr-2" />
              Relatórios Detalhados
            </Button>
            <Button variant="outline" size="sm">
              <TrendingUp className="h-4 w-4 mr-2" />
              Comparar Períodos
            </Button>
            <Button variant="outline" size="sm">
              <Users className="h-4 w-4 mr-2" />
              Análise de Clientes
            </Button>
            <Button variant="outline" size="sm">
              <CreditCard className="h-4 w-4 mr-2" />
              Métodos de Pagamento
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
