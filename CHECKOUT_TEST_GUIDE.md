# 🧪 Guia de Testes do Checkout

## 📋 Visão Geral

Este guia explica como testar o sistema de checkout do SupGateway, incluindo a criação de dados de teste e validação das funcionalidades.

## 🚀 Configuração Inicial

### 1. Instalar Dependências

```bash
pnpm install
```

### 2. Configurar Banco de Dados

Certifique-se de que o banco de dados está configurado e as migrações foram executadas:

```bash
# Executar migrações do Prisma
pnpm --filter @repo/database db:push
```

### 3. Criar Dados de Teste

Execute o script para criar dados de teste:

```bash
pnpm test:checkout:create
```

Este comando criará:
- ✅ Organização de teste
- ✅ Usuário professor
- ✅ Categoria de produtos
- ✅ Produto de curso completo
- ✅ Módulos e aulas
- ✅ Ofertas (order bumps)
- ✅ Usuário cliente

## 🧪 Páginas de Teste

### 1. Página Principal de Teste
**URL:** `/checkout/test`

Esta página mostra:
- Informações do produto de teste
- Ofertas disponíveis
- Métodos de pagamento
- Links para todas as páginas de teste

### 2. Checkout Completo
**URL:** `/checkout/{productId}`

Onde `{productId}` é o ID do produto criado pelo script de teste.

Funcionalidades para testar:
- ✅ Preenchimento do formulário de dados do cliente
- ✅ Seleção de métodos de pagamento
- ✅ Adição de order bumps (ofertas)
- ✅ Aplicação de cupons de desconto
- ✅ Cálculo automático do total
- ✅ Validação de campos obrigatórios

### 3. Páginas de Pagamento

#### Página de Sucesso
**URL:** `/checkout/success?orderId=test-order-123`

#### Pagamento PIX
**URL:** `/checkout/pix?orderId=test-order-123`

#### Pagamento Boleto
**URL:** `/checkout/boleto?orderId=test-order-123`

## 🔍 Checklist de Testes

### ✅ Validação de Campos

- [ ] **Email**: Formato válido obrigatório
- [ ] **Nome**: Mínimo 2 caracteres
- [ ] **CPF**: 11 dígitos obrigatório
- [ ] **Telefone**: Mínimo 10 dígitos
- [ ] **Cartão**: Número, nome, validade, CVV

### ✅ Funcionalidades do Checkout

- [ ] **Order Bumps**: Seleção/deseleção de ofertas
- [ ] **Cálculo de Total**: Atualização automática
- [ ] **Cupons**: Aplicação e remoção
- [ ] **Métodos de Pagamento**: Alternância entre opções
- [ ] **Responsividade**: Layout em mobile/desktop

### ✅ Fluxo de Pagamento

- [ ] **Submissão**: Envio do formulário
- [ ] **Redirecionamento**: Para página correta
- [ ] **Estados de Loading**: Durante processamento
- [ ] **Tratamento de Erros**: Mensagens amigáveis

### ✅ Dados do Schema

- [ ] **Produto**: Campos corretos do banco
- [ ] **Ofertas**: Estrutura `Offer` válida
- [ ] **Pedidos**: Criação de `Order` e `OrderItem`
- [ ] **Transações**: Registro de `Transaction`

## 🛠️ Scripts Disponíveis

### Criar Dados de Teste
```bash
pnpm test:checkout:create
```

### Limpar Dados de Teste
```bash
pnpm test:checkout:cleanup
```

## 📊 Dados de Teste Criados

### Produto
- **Nome**: Curso de Marketing Digital Completo
- **Preço**: R$ 297,00
- **Preço Comparação**: R$ 497,00
- **Tipo**: COURSE
- **Status**: PUBLISHED

### Ofertas (Order Bumps)
1. **E-book: Guia de Redes Sociais** - R$ 49,00
2. **Consultoria 1h** - R$ 197,00

### Usuários
- **Professor**: <EMAIL>
- **Cliente**: <EMAIL>

## 🐛 Troubleshooting

### Erro de Conexão com Banco
```bash
# Verificar se o banco está rodando
# Verificar variáveis de ambiente DATABASE_URL
```

### Erro de Migração
```bash
# Resetar e aplicar migrações
pnpm --filter @repo/database db:push --force-reset
```

### Dados de Teste Não Aparecem
```bash
# Recriar dados de teste
pnpm test:checkout:cleanup
pnpm test:checkout:create
```

## 📝 Logs de Debug

O sistema inclui logs detalhados para debug:

```typescript
console.log('=== SUBMIT FUNCTION CALLED ===');
console.log('Form data submitted:', data);
console.log('Processing payment...', paymentData);
```

## 🔗 Links Úteis

- [Documentação do Prisma](https://www.prisma.io/docs)
- [Next.js App Router](https://nextjs.org/docs/app)
- [Shadcn UI Components](https://ui.shadcn.com)
- [React Hook Form](https://react-hook-form.com)

## 📞 Suporte

Para problemas ou dúvidas:
1. Verificar logs do console
2. Validar dados no banco
3. Testar em ambiente limpo
4. Consultar documentação

---

**Última atualização:** $(date)
